import { BPlusTree, InsertResult, DeleteResult } from '../bPlusTree';

describe('BPlusTree 重构后功能测试', () => {
  let tree: BPlusTree;

  beforeEach(() => {
    tree = new BPlusTree(3);
  });

  describe('基本参数测试', () => {
    test('应该正确计算B+树参数', () => {
      expect(tree.getOrder()).toBe(3);
      expect(tree.getMaxKeys()).toBe(2);
      expect(tree.getMinKeys()).toBe(1);
      expect(tree.getSplitIndex()).toBe(1);
    });

    test('应该拒绝无效的阶数', () => {
      expect(() => new BPlusTree(2)).toThrow('B+树的阶数必须至少为3');
    });
  });

  describe('纯函数式插入测试', () => {
    test('应该能够插入单个键', () => {
      const result: InsertResult = tree.insertPure(10);
      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(tree.find(10)).toBe(true);
    });

    test('应该拒绝重复键', () => {
      tree.insertPure(10);
      const result: InsertResult = tree.insertPure(10);
      expect(result.success).toBe(false);
      expect(result.error).toBe('键 10 已存在');
    });

    test('应该能够插入多个键并触发分裂', () => {
      const keys = [10, 20, 30, 40, 50];
      keys.forEach(key => {
        const result = tree.insertPure(key);
        expect(result.success).toBe(true);
      });

      // 验证所有键都存在
      keys.forEach(key => {
        expect(tree.find(key)).toBe(true);
      });

      // 验证键的有序性
      const allKeys = tree.getAllKeys();
      expect(allKeys).toEqual([10, 20, 30, 40, 50]);
    });
  });

  describe('纯函数式删除测试', () => {
    beforeEach(() => {
      // 插入一些测试数据
      [10, 20, 30, 40, 50].forEach(key => tree.insertPure(key));
    });

    test('应该能够删除存在的键', () => {
      const result: DeleteResult = tree.deletePure(30);
      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(tree.find(30)).toBe(false);
    });

    test('应该拒绝删除不存在的键', () => {
      const result: DeleteResult = tree.deletePure(100);
      expect(result.success).toBe(false);
      expect(result.error).toBe('键 100 不存在');
    });

    test('删除后应该保持键的有序性', () => {
      tree.deletePure(30);
      const allKeys = tree.getAllKeys();
      expect(allKeys).toEqual([10, 20, 40, 50]);
    });
  });

  describe('可视化状态输出测试', () => {
    test('空树应该返回空的可视化状态', () => {
      const state = tree.getTreeStateForVisualization();
      expect(state.nodes).toHaveLength(0);
      expect(state.edges).toHaveLength(0);
    });

    test('单节点树应该返回正确的可视化状态', () => {
      tree.insertPure(10);
      const state = tree.getTreeStateForVisualization();
      
      expect(state.nodes).toHaveLength(1);
      expect(state.nodes[0].type).toBe('bPlusLeafNode');
      expect(state.nodes[0].data.isLeaf).toBe(true);
      expect(state.nodes[0].data.keys).toContain(10);
      expect(state.edges).toHaveLength(0);
    });

    test('多节点树应该包含正确的边', () => {
      // 插入足够的键来触发分裂
      [10, 20, 30, 40, 50].forEach(key => tree.insertPure(key));
      const state = tree.getTreeStateForVisualization();
      
      expect(state.nodes.length).toBeGreaterThan(1);
      expect(state.edges.length).toBeGreaterThan(0);
      
      // 验证有内部节点和叶子节点
      const hasInternalNode = state.nodes.some(node => !node.data.isLeaf);
      const hasLeafNode = state.nodes.some(node => node.data.isLeaf);
      expect(hasInternalNode).toBe(true);
      expect(hasLeafNode).toBe(true);
    });
  });

  describe('布局算法测试', () => {
    test('应该能够生成带布局的可视化状态', () => {
      [10, 20, 30, 40, 50].forEach(key => tree.insertPure(key));
      const layoutedState = tree.getLayoutedTreeState('TB');
      
      expect(layoutedState.nodes.length).toBeGreaterThan(0);
      
      // 验证所有节点都有位置信息
      layoutedState.nodes.forEach(node => {
        expect(typeof node.position.x).toBe('number');
        expect(typeof node.position.y).toBe('number');
      });
    });

    test('应该支持不同的布局方向', () => {
      [10, 20, 30].forEach(key => tree.insertPure(key));
      
      const tbLayout = tree.getLayoutedTreeState('TB');
      const lrLayout = tree.getLayoutedTreeState('LR');
      
      expect(tbLayout.nodes).toHaveLength(lrLayout.nodes.length);
      expect(tbLayout.edges).toHaveLength(lrLayout.edges.length);
    });
  });

  describe('向后兼容性测试', () => {
    test('原有的 getAllNodes 方法应该仍然工作', () => {
      [10, 20, 30].forEach(key => tree.insertPure(key));
      const allNodes = tree.getAllNodes();
      expect(allNodes.length).toBeGreaterThan(0);
      
      // 验证节点具有新的类型结构
      allNodes.forEach(node => {
        expect(node).toHaveProperty('id');
        expect(node).toHaveProperty('keys');
        expect(node).toHaveProperty('isLeaf');
        expect(node).toHaveProperty('level');
        expect(node).toHaveProperty('parent');
        
        if (node.isLeaf) {
          expect(node).toHaveProperty('values');
          expect(node).toHaveProperty('nextLeaf');
        } else {
          expect(node).toHaveProperty('children');
        }
      });
    });

    test('原有的 find 和 getAllKeys 方法应该仍然工作', () => {
      const keys = [10, 20, 30, 40, 50];
      keys.forEach(key => tree.insertPure(key));
      
      keys.forEach(key => {
        expect(tree.find(key)).toBe(true);
      });
      
      expect(tree.getAllKeys()).toEqual(keys);
    });
  });
});
