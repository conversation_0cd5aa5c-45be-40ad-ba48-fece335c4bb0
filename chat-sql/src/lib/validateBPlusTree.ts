import { BPlusTree } from './bPlusTree';

// 简单的测试断言函数
function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`断言失败: ${message}`);
  }
}

// 验证 B+ 树重构后的功能
export function validateBPlusTreeRefactoring(): void {
  console.log('🚀 开始验证 B+ 树重构后的功能...');

  try {
    // 测试 1: 基本参数计算
    console.log('📊 测试 1: 基本参数计算');
    const tree = new BPlusTree(3);
    assert(tree.getOrder() === 3, '阶数应该为 3');
    assert(tree.getMaxKeys() === 2, '最大键数应该为 2');
    assert(tree.getMinKeys() === 1, '最小键数应该为 1');
    assert(tree.getSplitIndex() === 1, '分裂索引应该为 1');
    console.log('✅ 基本参数计算测试通过');

    // 测试 2: 纯函数式插入
    console.log('📊 测试 2: 纯函数式插入');
    const insertResult1 = tree.insertPure(10);
    assert(insertResult1.success === true, '插入应该成功');
    assert(tree.find(10) === true, '应该能找到插入的键');

    const insertResult2 = tree.insertPure(10);
    assert(insertResult2.success === false, '重复插入应该失败');
    assert(insertResult2.error === '键 10 已存在', '应该返回正确的错误信息');
    console.log('✅ 纯函数式插入测试通过');

    // 测试 3: 多键插入和分裂
    console.log('📊 测试 3: 多键插入和分裂');
    const keys = [20, 30, 40, 50];
    keys.forEach(key => {
      const result = tree.insertPure(key);
      assert(result.success === true, `插入键 ${key} 应该成功`);
    });

    const allKeys = tree.getAllKeys();
    assert(allKeys.length === 5, '应该有 5 个键');
    assert(JSON.stringify(allKeys) === JSON.stringify([10, 20, 30, 40, 50]), '键应该按顺序排列');
    console.log('✅ 多键插入和分裂测试通过');

    // 测试 4: 纯函数式删除
    console.log('📊 测试 4: 纯函数式删除');
    const deleteResult1 = tree.deletePure(30);
    assert(deleteResult1.success === true, '删除应该成功');
    assert(tree.find(30) === false, '删除的键应该不存在');

    const deleteResult2 = tree.deletePure(100);
    assert(deleteResult2.success === false, '删除不存在的键应该失败');
    assert(deleteResult2.error === '键 100 不存在', '应该返回正确的错误信息');

    const remainingKeys = tree.getAllKeys();
    assert(JSON.stringify(remainingKeys) === JSON.stringify([10, 20, 40, 50]), '剩余键应该正确');
    console.log('✅ 纯函数式删除测试通过');

    // 测试 5: 可视化状态输出
    console.log('📊 测试 5: 可视化状态输出');
    const visualState = tree.getTreeStateForVisualization();
    assert(visualState.nodes.length > 0, '应该有节点');
    assert(Array.isArray(visualState.edges), '应该有边数组');

    // 验证节点数据结构
    visualState.nodes.forEach(node => {
      assert(typeof node.id === 'string', '节点应该有 ID');
      assert(typeof node.type === 'string', '节点应该有类型');
      assert(Array.isArray(node.data.keys), '节点应该有键数组');
      assert(typeof node.data.isLeaf === 'boolean', '节点应该有 isLeaf 属性');
      assert(typeof node.data.level === 'number', '节点应该有 level 属性');
    });
    console.log('✅ 可视化状态输出测试通过');

    // 测试 6: 布局算法
    console.log('📊 测试 6: 布局算法');
    const layoutedState = tree.getLayoutedTreeState('TB');
    assert(layoutedState.nodes.length === visualState.nodes.length, '布局后节点数量应该相同');
    
    // 验证所有节点都有位置信息
    layoutedState.nodes.forEach(node => {
      assert(typeof node.position.x === 'number', '节点应该有 x 坐标');
      assert(typeof node.position.y === 'number', '节点应该有 y 坐标');
    });

    // 测试不同布局方向
    const lrLayoutedState = tree.getLayoutedTreeState('LR');
    assert(lrLayoutedState.nodes.length === layoutedState.nodes.length, 'LR 布局节点数量应该相同');
    console.log('✅ 布局算法测试通过');

    // 测试 7: 向后兼容性
    console.log('📊 测试 7: 向后兼容性');
    const allNodes = tree.getAllNodes();
    assert(allNodes.length > 0, '应该能获取所有节点');
    
    // 验证节点结构
    allNodes.forEach(node => {
      assert(typeof node.id === 'string', '节点应该有 ID');
      assert(Array.isArray(node.keys), '节点应该有键数组');
      assert(typeof node.isLeaf === 'boolean', '节点应该有 isLeaf 属性');
      assert(typeof node.level === 'number', '节点应该有 level 属性');
      
      if (node.isLeaf) {
        assert('values' in node, '叶子节点应该有 values 属性');
        assert('nextLeaf' in node, '叶子节点应该有 nextLeaf 属性');
      } else {
        assert('children' in node, '内部节点应该有 children 属性');
      }
    });
    console.log('✅ 向后兼容性测试通过');

    // 测试 8: 错误处理
    console.log('📊 测试 8: 错误处理');
    try {
      new BPlusTree(2);
      assert(false, '应该抛出错误');
    } catch (error) {
      assert(error instanceof Error, '应该抛出 Error 实例');
      assert((error as Error).message === 'B+树的阶数必须至少为3', '应该有正确的错误信息');
    }
    console.log('✅ 错误处理测试通过');

    console.log('🎉 所有测试通过！B+ 树重构成功！');
    
    // 输出性能统计
    console.log('\n📈 性能统计:');
    console.log(`- 节点总数: ${allNodes.length}`);
    console.log(`- 叶子节点数: ${allNodes.filter(n => n.isLeaf).length}`);
    console.log(`- 内部节点数: ${allNodes.filter(n => !n.isLeaf).length}`);
    console.log(`- 树的高度: ${Math.max(...allNodes.map(n => n.level)) + 1}`);
    console.log(`- 键的总数: ${tree.getAllKeys().length}`);

  } catch (error) {
    console.error('❌ 测试失败:', error instanceof Error ? error.message : error);
    throw error;
  }
}

// 如果直接运行此文件，执行验证
if (typeof window === 'undefined' && require.main === module) {
  validateBPlusTreeRefactoring();
}
