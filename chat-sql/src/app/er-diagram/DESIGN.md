# ER-Graph 模块设计文档

本文档详细描述了 ChatSQL 应用中 ER-Graph 模块的功能、UI 布局和交互设计，其核心定位是一个**交互式的ER图可视化建模工具**。

## 1. 整体布局与导航

ER-Graph 模块作为应用的三大核心功能之一，与 Coding 和 BPlus 并列。

- **顶部导航 (Topbar)**: 在应用主标题 "ChatSQL" 右侧，提供三个并排的导航入口：`Coding` | `ER-Graph` | `BPlus`。用户通过点击 `ER-Graph` 进入本模块。
- **主界面布局**: ER-Graph 模块采用经典的三栏式布局：
    - **左侧边栏 (Sidebar)**: 提供全局操作和对象导航。
    - **中间画布 (Canvas)**: 用户的核心工作区，用于 ER 图的绘制、布局和交互。
    - **右侧详情栏 (Inspector)**: 上下文感知面板，用于显示和编辑画布中选中元素的详细信息。

---

## 2. 左侧边栏 (Sidebar) 功能详解

左侧边栏提供五个核心功能按钮。

### 2.1. 新建图表 (New Diagram)

- **触发方式**: 点击 "新建图表" 按钮。
- **交互**: 弹出一个模态框 (Modal)。
- **模态框内容**:
    - **选项一：从空白创建**: 创建一个没有任何实体和关系的空图表。
    - **选项二：从模板创建**: 提供一个预设模板列表（如 "学生选课系统"）。用户选择一个模板后，画布将加载对应的示例 ER 图。
- **操作**: 用户选择一项并确认后，模态框关闭，中间画布更新为新的图表状态。

### 2.2. 打开图表 (Open Diagram)

- **触发方式**: 点击 "打开图表" 按钮。
- **交互**: 弹出一个模态框，展示历史图表列表。
- **列表展示信息**: 每一行代表一个已保存的图表，包含以下列：图表名称、创建时间、最后修改时间、实体数、关系数。
- **操作**: 用户可以点击某一行来加载对应的 ER 图到画布，或删除某个历史记录。

### 2.3. 组件库 (Component Library)

- **触发方式**: 点击 "组件库" 按钮。
- **交互**: 右侧详情栏切换到“组件库”视图。这是用户**创建新节点**的主要入口。
- **组件库内容**: 显示可用于绘制 ER 图的基本形状。用户可以从这里将组件**拖拽**到中间的画布上。
    - **强实体集 (Strong Entity)**: 标准的实线矩形。
    - **弱实体集 (Weak Entity)**: 双实线矩形。
    - **关系 (Relationship)**: 菱形。

### 2.4. 实体 (Entities)

- **触发方式**: 点击 "实体" 按��。
- **交互**: 右侧详情栏切换到“实体列表”视图，用于快速导航和编辑。
- **实体列表**:
    - 以列表形式展示当前图表中所有的实体集。
    - 每个实体条目都是可展开的 (Accordion)。
    - **展开后**: 显示该实体的所有属性列表。
        - **属性交互**: 可编辑属性名、类型、是否为主键；可拖拽排序；可删除。
    - **删除实体**: 每个实体条目最右侧有删除按钮（需二次确认）。

### 2.5. 关系 (Relationships)

- **触发方式**: 点击 "关系" 按钮。
- **交互**: 右侧详情栏切换到“关系列表”视图。
- **关系列表**:
    - 以列表形式展示所有的关系。
    - **悬浮交互**: 鼠标悬浮时显示定位、编辑、删除按钮。
        - **定位**: 点击后，画布将该关系置于视图中心并高亮相关元素。
    - **展开后**: 显示该关系的所有约束连接 (`connections`)。
        - **约束修改**: 点击基数标签（如 `'1..*'`）可从下拉菜单中选择新的基数。



左下角还有两个额外的按钮, 分别是“帮助” 和 “github 仓库”, 也就是coding主页面的样式, 但是注意“帮助”的内容肯定是不同的, 现在可以让帮助点击之后弹出的modal设置为占位符.





---

## 3. 中间画布 (Canvas) 交互详解

画布是用户与 ER 图直接交互的核心区域。

### 3.1. 悬浮与连接 (Hover & Connect)

- **触发方式**: 当鼠标悬浮到一个实体或关系节点上时。
- **交互**: 节点的四个边中点会显示出透明的连接点（Handles）。
- **创建连接**: 用户可以按住连接点拖拽出一条线，并只能连接到另一种类型的节点上（实体 ↔ 关系）。连接成功后，自动创建一条默认基数为 `'1..*'` 的边。

### 3.2. 双击交互 (Double Click)

- **双击节点名称**: 直接进入内联编辑模式，快速重命名。
- **双击实体内部**: 自动切换并定位到右侧详情栏中对应的实体条目，方便进行详细的属性编辑。

### 3.3. 单击与高亮 (Single Click & Highlight)

- **单击节点**: 节点被选中，相关边被高亮，右侧详情栏显示其详细信息供编辑。
- **单击画布空白处**: 取消所有选中和高亮状态。

### 3.4. 拖拽 (Drag)

- 用户可以拖拽节点以自由布局。节点的位置信息 (`position`) 会被实时更新并保存。

---

## 4. 技术实现要点 (Technical Design)

本章节概述了实现上述**交互式绘制功能**所需的核心技术架构。

### 4.1. 数据持久化

- **存储方案**: 采用 **IndexedDB** 作为客户端存储方案。
- **存储内容**: 每个ER图作为一个独立的记录存储。存储的对象是符合 `ERDiagramData` 接口规范的、代表用户绘制结果的完整 JSON。
- **实现参考**: 可复用或借鉴现有 `coding` 模块中与 IndexedDB 交互的逻辑，创建专门的ER图存储服务。

### 4.2. 状态管理

采用 React Context API 进行跨组件的状态管理，以确保“单一事实来源”。

- **`ERDiagramProvider`**: 一个总的上下文提供者，内部包含以下所有状态。
- **核心状态**:
    - **`currentDiagramId: string | null`**: 当前正在编辑的图表的ID。
    - **`diagramData: ERDiagramData | null`**: **核心数据源**。用户的**所有绘制操作**（如添加节点、修改属性、连接关系）最终都体现为对这个状态对象的修改。
    - **`activeSidebarTab: 'components' | 'entities' | 'relationships'`**: 标识左侧边栏的激活标签，用于控制右侧详情栏的显示。
    - **`selectedElementId: string | null`**: 画布上被单击选中的节点或边的ID。
- **状态更新**:
    - 强烈推荐使用 **`useReducer`** hook 来管理 `diagramData` 的复杂状态变更。定义清晰的 `actions` (如 `ADD_NODE_FROM_COMPONENT_LIBRARY`, `UPDATE_ENTITY_NAME`, `CREATE_CONNECTION`) 来保证状态更新的可预测性。

### 4.3. 核心组件与数据流

- **`ERDiagramCanvas` (中间画布)**:
    - **主要职责**: 将 `diagramData` 状态**映射**为 React Flow 需要的 `nodes` 和 `edges` 数组进行渲染。这个映射是一个纯粹的可视化转换，不包含复杂的业务逻辑。
    - **交互处理**: 监听 React Flow 的事件（如 `onNodesChange` for dragging, `onConnect` for new edges, `onNodeClick` for selection），并将这些用户交互转换为状态更新的 `actions`，派发给 reducer。
- **`Sidebar` (左侧边栏)**:
    - 触发全局操作（新建/打开）。
    - 通过 `setActiveSidebarTab` 来改变 `activeSidebarTab` 状态。
- **`Inspector` (右侧详情栏)**:
    - 订阅 `activeSidebarTab` 和 `selectedElementId` 状态以显示相应内容。
    - 用户在此处进行的修改（如编辑属性名、改变基数），最终也会转换为 `actions` 派发给 reducer，以更新全局的 `diagramData`。

### 4.4. 性能优化

- **Memoization**: 用于将 `diagramData` 映射到 `nodes` 和 `edges` 的函数必须使用 `useMemo` 进行包裹，确保只有在 `diagramData` 变化时才重新计算，避免不必要的渲染。
- **组件拆分**: 将UI拆分为更小的、独立的组件，并适当使用 `React.memo` 来防止不必要的子组件重渲染，尤其是在右侧详情栏中。

---

## 5. 技术栈

- **前端框架**: React 18 + TypeScript
- **UI组件库**: Antd (Ant Design)
- **图形库**: @xyflow/react (React Flow)
- **状态管理**: React Context + useReducer
- **样式**: CSS Modules
- **构建工具**: Next.js 15

## 6. 开发规范

- 使用 TypeScript 严格模式，避免 any 类型
- 组件采用函数式组件 + Hooks
- 样式使用 CSS Modules，支持暗色主题
- 遵循 React 最佳实践和性能优化

---

## 7. 继续开发指南

### 7.1. 当前完成状态

✅ **阶段一：基础架构和拖拽功能**
- 文件架构重构完成，组件已移动到正确位置
- ERDiagramContext 状态管理完善，支持实体和关系的增删改查
- 拖拽功能完整实现，支持从组件库拖拽到画布创建节点
- 视觉反馈和用户体验优化完成

### 7.2. 下一步开发任务

#### 阶段二：节点编辑功能 🚧

**任务 2.1：实现双击节点重命名**
```typescript
// 需要在 ERDiagram 组件中添加双击事件处理
// 位置：src/components/ERDiagram/ERDiagram.tsx

interface NodeEditProps {
  nodeId: string;
  currentName: string;
  onSave: (newName: string) => void;
  onCancel: () => void;
}

// 实现内联编辑组件
const InlineEditor: React.FC<NodeEditProps> = ({ nodeId, currentName, onSave, onCancel }) => {
  // 使用 Input 组件实现内联编辑
  // 支持 Enter 保存，Esc 取消
  // 自动聚焦和选中文本
};
```

**任务 2.2：节点选中状态管理**
```typescript
// 扩展 ERDiagramContext
interface ERDiagramState {
  // ... 现有状态
  selectedNodeId: string | null;
  editingNodeId: string | null;
  nodeEditMode: 'none' | 'rename' | 'properties';
}

// 新增 Action 类型
type ERDiagramAction =
  | { type: 'SELECT_NODE'; payload: { nodeId: string | null } }
  | { type: 'START_EDIT_NODE'; payload: { nodeId: string; mode: 'rename' | 'properties' } }
  | { type: 'FINISH_EDIT_NODE' }
  | { type: 'RENAME_NODE'; payload: { nodeId: string; newName: string } };
```

**任务 2.3：右侧属性编辑面板**
```typescript
// 创建 PropertyEditor 组件
// 位置：src/components/ERDiagram/UI/PropertyEditor.tsx

interface PropertyEditorProps {
  selectedElement: EREntity | ERRelationship | null;
  onUpdateEntity: (id: string, updates: Partial<EREntity>) => void;
  onUpdateRelationship: (id: string, updates: Partial<ERRelationship>) => void;
}

// 实现功能：
// 1. 实体属性列表的增删改查
// 2. 属性类型选择（VARCHAR, INT, DATE等）
// 3. 主键、外键、必填等约束设置
// 4. 关系约束条件编辑
```

#### 阶段三：连接功能 🔄

**任务 3.1：节点连接点显示**
```typescript
// 修改 EntityNode 和 DiamondNode 组件
// 位置：src/components/ERDiagram/EntityNode.tsx, DiamondNode.tsx

// 添加 Handle 组件用于连接
import { Handle, Position } from '@xyflow/react';

// 在节点悬浮时显示连接点
const [isHovered, setIsHovered] = useState(false);

// 实体节点：上下左右四个连接点
// 关系节点：上下左右四个连接点
```

**任务 3.2：连线创建和管理**
```typescript
// 扩展数据结构支持连线
interface ERConnection {
  id: string;
  sourceEntityId: string;
  targetRelationshipId: string;
  sourceHandle: string;
  targetHandle: string;
  cardinality: '1' | 'N' | 'M';
  participation: 'total' | 'partial';
  label?: string;
}

// 在 ERDiagramData 中添加 connections 字段
interface ERDiagramData {
  entities: EREntity[];
  relationships: ERRelationship[];
  connections: ERConnection[]; // 新增
  metadata: ERMetadata;
}
```

**任务 4.3：SQL 代码生成**
```typescript
// 创建 SQL 生成器
// 位置：src/components/ERDiagram/utils/sqlGenerator.ts

interface SQLGeneratorOptions {
  dialect: 'mysql' | 'postgresql' | 'sqlite' | 'mssql';
  includeConstraints: boolean;
  includeIndexes: boolean;
  tablePrefix?: string;
}

// 实现功能：
// 1. 根据 ER 图生成 CREATE TABLE 语句
// 2. 生成外键约束
// 3. 生成索引语句
// 4. 支持多种数据库方言
```

### 7.3. 开发优先级建议

1. **立即开始**：阶段二的节点编辑功能（用户体验核心）
2. **次要优先**：阶段三的连接功能（ER图核心逻辑）
3. **后续完善**：阶段四的高级功能（增值特性）

### 7.4. 技术实现要点

#### 状态管理最佳实践
```typescript
// 使用 immer 优化状态更新（可选）
import { produce } from 'immer';

const erDiagramReducer = produce((draft, action) => {
  switch (action.type) {
    case 'UPDATE_ENTITY':
      const entity = draft.diagramData.entities.find(e => e.id === action.payload.id);
      if (entity) {
        Object.assign(entity, action.payload.updates);
      }
      break;
  }
});
```

#### 性能优化建议
```typescript
// 使用 React.memo 优化节点渲染
export const EntityNode = React.memo<NodeProps>(({ data, selected }) => {
  // 组件实现
});

// 使用 useMemo 优化计算
const processedNodes = useMemo(() => {
  return entities.map(entity => ({
    id: entity.id,
    type: 'entity',
    position: entity.position,
    data: entity
  }));
}, [entities]);
```

#### 类型安全保障
```typescript
// 严格的类型定义
interface ERDiagramHooks {
  useEntityEditor: (entityId: string) => {
    entity: EREntity | null;
    updateEntity: (updates: Partial<EREntity>) => void;
    deleteEntity: () => void;
  };

  useRelationshipEditor: (relationshipId: string) => {
    relationship: ERRelationship | null;
    updateRelationship: (updates: Partial<ERRelationship>) => void;
    deleteRelationship: () => void;
  };
}
```

### 7.5. 测试策略

#### 单元测试重点
- Context 状态管理逻辑
- 节点工厂函数
- 数据验证函数
- SQL 生成器

#### 集成测试重点
- 拖拽创建节点流程
- 节点编辑保存流程
- 连线创建删除流程
- 数据持久化流程

#### E2E 测试场景
- 完整的 ER 图创建流程
- 复杂图表的编辑操作
- 导入导出功能验证
- 多浏览器兼容性测试

### 7.6. 部署和发布

#### 构建优化
```bash
# 确保构建成功
npm run build

# 类型检查
npm run type-check

# 代码质量检查
npm run lint

# 单元测试
npm run test
```

#### 性能监控
- 使用 React DevTools Profiler 监控渲染性能
- 监控大型 ER 图的加载时间
- 优化拖拽操作的响应速度

### 7.7. 文档完善

#### 用户文档
- ER 图建模教程
- 功能使用指南
- 快捷键说明
- 常见问题解答

#### 开发文档
- 组件 API 文档
- 状态管理架构说明
- 扩展开发指南
- 贡献者指南

### 7.8. 开发提示词模板

当需要继续开发时，可以使用以下提示词：

```
继续实现 ER-Graph 模块的 [阶段名称] 功能，按照 DESIGN.md 中的要求完成以下具体任务：

**当前任务**：[具体任务名称，如"实现双击节点重命名"]

**技术要求**：
1. 使用现有的 ERDiagramContext 状态管理
2. 确保所有修改符合 TypeScript 类型约束，避免使用 any 类型
3. 保持与现有组件架构的一致性
4. 实现过程中参考现有的 ERDiagram 组件实现
5. 每完成一个功能点后进行构建测试，确保 npm run build 通过

**实现步骤**：
1. [具体步骤1]
2. [具体步骤2]
3. [具体步骤3]

请逐步实现每个功能，每完成一个功能点后进行测试验证。
```

---

**开发提示**：建议按照上述阶段顺序逐步实现，每完成一个阶段都进行充分测试，确保功能稳定后再进入下一阶段。重点关注用户体验和代码质量，保持良好的 TypeScript 类型安全。

**当前状态**：✅ 阶段一完成，可以开始阶段二的节点编辑功能开发。

**任务 3.3：连线样式和标签**
```typescript
// 创建自定义边组件
// 位置：src/components/ERDiagram/CustomEdge.tsx

interface CustomEdgeProps extends EdgeProps {
  data: {
    cardinality: string;
    participation: 'total' | 'partial';
    label?: string;
  };
}

// 实现功能：
// 1. 基数标签显示（1, N, M）
// 2. 参与约束显示（单线/双线）
// 3. 连线标签编辑
// 4. 连线删除功能
```

#### 阶段四：高级功能 ⚡

**任务 4.1：数据持久化**
```typescript
// 创建 IndexedDB 存储服务
// 位置：src/services/erDiagramStorage.ts

interface ERDiagramStorage {
  saveDiagram: (diagram: ERDiagramData) => Promise<string>;
  loadDiagram: (id: string) => Promise<ERDiagramData>;
  listDiagrams: () => Promise<ERDiagramMetadata[]>;
  deleteDiagram: (id: string) => Promise<void>;
  exportDiagram: (id: string, format: 'json' | 'sql' | 'png') => Promise<Blob>;
}

// 实现功能：
// 1. 本地存储 ER 图数据
// 2. 图表列表管理
// 3. 导入导出功能
// 4. 自动保存机制
```

**任务 4.2：布局算法优化**
```typescript
// 创建自动布局服务
// 位置：src/components/ERDiagram/utils/layoutAlgorithm.ts

interface LayoutOptions {
  algorithm: 'hierarchical' | 'force' | 'circular';
  spacing: { x: number; y: number };
  direction: 'TB' | 'BT' | 'LR' | 'RL';
}

// 实现功能：
// 1. 自动排列实体和关系
// 2. 避免节点重叠
// 3. 优化连线路径
// 4. 支持多种布局算法
```
