const { execSync } = require('child_process');

console.log('🔧 编译 TypeScript 文件...');

try {
  // 编译 TypeScript 文件
  execSync('npx tsc --target es2020 --module commonjs --esModuleInterop --skipLibCheck src/lib/bPlusTree.ts --outDir dist', {
    cwd: __dirname,
    stdio: 'inherit'
  });

  execSync('npx tsc --target es2020 --module commonjs --esModuleInterop --skipLibCheck src/lib/validateBPlusTree.ts --outDir dist', {
    cwd: __dirname,
    stdio: 'inherit'
  });

  console.log('✅ TypeScript 编译完成');

  // 运行验证
  console.log('\n🧪 运行 B+ 树索引键更新修复验证...\n');
  
  const { validateBPlusTreeRefactoring } = require('./dist/validateBPlusTree');
  validateBPlusTreeRefactoring();

  console.log('\n🎉 所有测试通过！B+ 树删除操作后父节点索引键更新问题已修复！');

} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
} finally {
  // 清理编译文件
  try {
    execSync('rm -rf dist', { cwd: __dirname });
  } catch (e) {
    // 忽略清理错误
  }
}
