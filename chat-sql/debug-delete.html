<!DOCTYPE html>
<html>
<head>
    <title>B+树删除测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .node { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .leaf { background-color: #e8f5e8; }
        .internal { background-color: #e8e8f5; }
        .error { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <h1>B+树删除操作测试</h1>
    <p>测试用例：初始键值 [10, 20, 5, 15, 25, 3, 7]，删除键值 10</p>
    
    <div id="output"></div>
    
    <script type="module">
        // 模拟 BPlusTree 类的简化版本来测试逻辑
        class TestBPlusTree {
            constructor(order = 3) {
                this.order = order;
                this.root = null;
                this.nodeCounter = 0;
                this.allNodes = new Map();
            }
            
            createNode(isLeaf, level) {
                const node = {
                    id: `node-${this.nodeCounter++}`,
                    keys: [],
                    pointers: [],
                    isLeaf,
                    level,
                    parent: null,
                    next: null
                };
                this.allNodes.set(node.id, node);
                return node;
            }
            
            // 简化的插入方法（仅用于构建测试树）
            simpleInsert(key) {
                if (!this.root) {
                    this.root = this.createNode(true, 0);
                    this.root.keys.push(key);
                    return;
                }
                
                // 找到叶子节点并插入
                let current = this.root;
                while (!current.isLeaf) {
                    let childIndex = 0;
                    while (childIndex < current.keys.length && key >= current.keys[childIndex]) {
                        childIndex++;
                    }
                    const childId = current.pointers[childIndex];
                    current = this.allNodes.get(childId);
                }
                
                // 插入到叶子节点
                let insertIndex = 0;
                while (insertIndex < current.keys.length && current.keys[insertIndex] < key) {
                    insertIndex++;
                }
                current.keys.splice(insertIndex, 0, key);
                
                // 简化的分裂处理
                if (current.keys.length >= this.order) {
                    this.splitLeaf(current);
                }
            }
            
            splitLeaf(node) {
                const mid = Math.ceil(node.keys.length / 2);
                const newNode = this.createNode(true, node.level);
                newNode.keys = node.keys.splice(mid);
                newNode.next = node.next;
                node.next = newNode.id;
                
                // 如果没有父节点，创建新的根节点
                if (!node.parent) {
                    const newRoot = this.createNode(false, node.level + 1);
                    newRoot.keys.push(newNode.keys[0]);
                    newRoot.pointers.push(node.id, newNode.id);
                    node.parent = newRoot.id;
                    newNode.parent = newRoot.id;
                    this.root = newRoot;
                }
            }
            
            getAllNodes() {
                return Array.from(this.allNodes.values());
            }
            
            printTree() {
                const output = document.getElementById('output');
                output.innerHTML = '';
                
                const nodes = this.getAllNodes();
                nodes.sort((a, b) => b.level - a.level || a.id.localeCompare(b.id));
                
                nodes.forEach(node => {
                    const div = document.createElement('div');
                    div.className = `node ${node.isLeaf ? 'leaf' : 'internal'}`;
                    div.innerHTML = `
                        <strong>${node.id}</strong> (Level ${node.level}, ${node.isLeaf ? 'Leaf' : 'Internal'})<br>
                        Keys: [${node.keys.join(', ')}]<br>
                        ${node.pointers.length > 0 ? `Pointers: [${node.pointers.join(', ')}]` : ''}
                    `;
                    output.appendChild(div);
                });
            }
            
            checkForKey10() {
                const nodes = this.getAllNodes();
                let hasKey10 = false;
                let nodesWithKey10 = [];
                
                nodes.forEach(node => {
                    if (node.keys.includes(10)) {
                        hasKey10 = true;
                        nodesWithKey10.push(node.id);
                    }
                });
                
                const resultDiv = document.createElement('div');
                if (hasKey10) {
                    resultDiv.className = 'error';
                    resultDiv.innerHTML = `❌ 错误：以下节点中仍然存在键值 10: ${nodesWithKey10.join(', ')}`;
                } else {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = '✅ 成功：所有节点中的键值 10 都已被正确更新';
                }
                
                document.getElementById('output').appendChild(resultDiv);
            }
        }
        
        // 运行测试
        const tree = new TestBPlusTree(3);
        
        // 插入初始数据
        const keys = [10, 20, 5, 15, 25, 3, 7];
        console.log('插入键值:', keys);
        keys.forEach(key => tree.simpleInsert(key));
        
        console.log('插入后的树结构:');
        tree.printTree();
        
        // 添加说明
        const infoDiv = document.createElement('div');
        infoDiv.innerHTML = `
            <h2>插入后的树结构</h2>
            <p>请在浏览器中打开 <a href="http://localhost:3001/bplus-test" target="_blank">http://localhost:3001/bplus-test</a> 进行实际测试</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>观察初始树结构，注意键值 10 在哪些节点中出现</li>
                <li>在删除输入框中输入 10</li>
                <li>点击删除按钮</li>
                <li>观察删除后的树结构，确认所有节点中的键值 10 都被正确更新</li>
            </ol>
        `;
        document.getElementById('output').appendChild(infoDiv);
    </script>
</body>
</html>
