// 测试B+树删除操作中索引键更新的修复效果
const { BPlusTree } = require('./src/lib/bPlusTree.ts');

function testDeleteFirstKey() {
  console.log('=== 测试删除叶子节点首键的索引更新 ===');
  
  // 创建B+树，阶数为3
  const tree = new BPlusTree(3);
  
  // 插入测试数据：[10, 20, 5, 15, 25, 3, 7]
  const keys = [10, 20, 5, 15, 25, 3, 7];
  console.log('插入键值:', keys);
  
  keys.forEach(key => {
    const generator = tree.insert(key);
    let result = generator.next();
    while (!result.done) {
      result = generator.next();
    }
  });
  
  // 打印插入后的树结构
  console.log('\n插入后的树结构:');
  const allNodes = tree.getAllNodes();
  allNodes.forEach(node => {
    console.log(`节点 ${node.id}: keys=[${node.keys.join(', ')}], isLeaf=${node.isLeaf}, level=${node.level}`);
  });
  
  // 删除键值10（这是某个叶子节点的首键）
  console.log('\n删除键值 10...');
  const deleteGenerator = tree.delete(10);
  let deleteResult = deleteGenerator.next();
  while (!deleteResult.done) {
    console.log('动画步骤:', deleteResult.value);
    deleteResult = deleteGenerator.next();
  }
  
  // 打印删除后的树结构
  console.log('\n删除后的树结构:');
  const allNodesAfter = tree.getAllNodes();
  allNodesAfter.forEach(node => {
    console.log(`节点 ${node.id}: keys=[${node.keys.join(', ')}], isLeaf=${node.isLeaf}, level=${node.level}`);
  });
  
  // 检查是否还有键值10存在于任何节点中
  let hasKey10 = false;
  allNodesAfter.forEach(node => {
    if (node.keys.includes(10)) {
      hasKey10 = true;
      console.log(`❌ 错误：节点 ${node.id} 中仍然存在键值 10`);
    }
  });
  
  if (!hasKey10) {
    console.log('✅ 成功：所有节点中的键值 10 都已被正确更新');
  }
  
  // 验证树的完整性
  console.log('\n验证树的完整性...');
  const allKeys = tree.getAllKeys();
  console.log('所有键值:', allKeys);
  
  if (allKeys.includes(10)) {
    console.log('❌ 错误：getAllKeys() 仍然返回键值 10');
  } else {
    console.log('✅ 成功：getAllKeys() 不再包含键值 10');
  }
}

// 运行测试
testDeleteFirstKey();
